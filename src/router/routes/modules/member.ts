import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const MEMBER: AppRouteRecordRaw = {
  path: '/member',
  name: 'Member',
  meta: {
    locale: '会员',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'black-list',
      name: 'MerchantBlackList',
      component: () => import('@/views/black-list/list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员黑名单',
      },
    },
    {
      path: 'black-card-list',
      name: 'MerchantBlackCardList',
      component: () => import('@/views/black-card-list/list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员卡禁用',
      },
    },
    {
      path: 'merchant-list',
      name: 'MemberMerchantList',
      component: () => import('@/views/member/merchant-list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员列表',
      },
    },
    {
      path: 'followup-list',
      name: 'MemberFollowupList',
      component: () => import('@/views/member/followup-list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员跟进列表',
      },
    },
    {
      path: 'followup-coach-detail/:coachId?',
      name: 'MemberFollowupCoachDetail',
      component: () => import('@/views/member/components/followup-coach-detail.vue'),
      props: true,
      meta: {
        keepAlive: true,
        locale: '教练跟进详情',
      },
    },
  ],
};

export default MEMBER;
