import request from '@/request/index';

// 跟进教练数据接口
export interface FollowupCoachData {
  coach_id: number;
  coach_name: string;
  follow_user_num: number;
  follow_num: number;
}

// 搜索参数接口
export interface FollowupCoachSearchParams {
  start_time?: string;
  end_time?: string;
  coach_id?: string;
  page_no?: number;
  page_size?: number;
  _export?: number;
}

// 响应数据接口
export interface FollowupCoachListResponse {
  list: FollowupCoachData[];
  total: number;
}

// 获取跟进教练列表
export function getFollowupCoachList() {
  return request<FollowupCoachListResponse>({
    url: '/Web/Statistics/getCoachFollowStatistics',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 导出跟进教练数据
export function exportFollowupCoachData() {
  return request({
    url: '/Web/Statistics/getCoachFollowStatisticsExport',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}
